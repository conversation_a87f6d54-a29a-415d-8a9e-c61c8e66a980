name: diffusionsdf
channels:
  - pytorch
  - nvidia
  - conda-forge
  - defaults
dependencies:
  - _libgcc_mutex=0.1=main
  - _openmp_mutex=5.1=1_gnu
  - blas=1.0=mkl
  - brotlipy=0.7.0=py39h27cfd23_1003
  - bzip2=1.0.8=h7b6447c_0
  - ca-certificates=2022.4.26=h06a4308_0
  - certifi=2022.5.18.1=py39h06a4308_0
  - cffi=1.15.0=py39hd667e15_1
  - charset-normalizer=2.0.4=pyhd3eb1b0_0
  - cryptography=37.0.1=py39h9ce1e76_0
  - cuda-toolkit=12.2
  - ffmpeg=4.3=hf484d3e_0
  - freetype=2.11.0=h70c0345_0
  - giflib=5.2.1=h7b6447c_0
  - gmp=6.2.1=h295c915_3
  - gnutls=3.6.15=he1e5248_0
  - idna=3.3=pyhd3eb1b0_0
  - intel-openmp=2021.4.0=h06a4308_3561
  - jpeg=9e=h7f8727e_0
  - lame=3.100=h7b6447c_0
  - lcms2=2.12=h3be6417_0
  - ld_impl_linux-64=2.38=h1181459_1
  - libffi=3.3=he6710b0_2
  - libgcc-ng=11.2.0=h1234567_1
  - libgomp=11.2.0=h1234567_1
  - libiconv=1.16=h7f8727e_2
  - libidn2=2.3.2=h7f8727e_0
  - libpng=1.6.37=hbc83047_0
  - libstdcxx-ng=11.2.0=h1234567_1
  - libtasn1=4.16.0=h27cfd23_0
  - libtiff=4.2.0=h2818925_1
  - libunistring=0.9.10=h27cfd23_0
  - libuv=1.40.0=h7b6447c_0
  - libwebp=1.2.2=h55f646e_0
  - libwebp-base=1.2.2=h7f8727e_0
  - lz4-c=1.9.3=h295c915_1
  - mkl=2021.4.0=h06a4308_640
  - mkl-service=2.4.0=py39h7f8727e_0
  - mkl_fft=1.3.1=py39hd3c417c_0
  - mkl_random=1.2.2=py39h51133e4_0
  - ncurses=6.3=h7f8727e_2
  - nettle=3.7.3=hbbd107a_1
  - numpy=1.22.3=py39he7a7128_0
  - numpy-base=1.22.3=py39hf524024_0
  - openh264=2.1.1=h4ff587b_0
  - openssl=1.1.1o=h7f8727e_0
  - pillow=9.0.1=py39h22f2fdc_0
  - pip=21.2.4=py39h06a4308_0
  - pycparser=2.21=pyhd3eb1b0_0
  - pyopenssl=22.0.0=pyhd3eb1b0_0
  - pysocks=1.7.1=py39h06a4308_0
  - python=3.9.12=h12debd9_1
  - pytorch=2.1.0
  - pytorch-mutex=1.0=cuda
  - readline=8.1.2=h7f8727e_1
  - requests=2.27.1=pyhd3eb1b0_0
  - setuptools=61.2.0=py39h06a4308_0
  - six=1.16.0=pyhd3eb1b0_1
  - sqlite=3.38.3=hc218d9a_0
  - tk=8.6.12=h1ccaba5_0
  - torchaudio=2.1.0
  - torchvision=0.16.0
  - typing_extensions=4.1.1=pyh06a4308_0
  - tzdata=2022a=hda174b7_0
  - urllib3=1.26.9=py39h06a4308_0
  - wheel=0.37.1=pyhd3eb1b0_0
  - xz=5.2.5=h7f8727e_1
  - zlib=1.2.12=h7f8727e_2
  - zstd=1.5.2=ha4553b6_0
  - pip:
    - absl-py==1.1.0
    - addict==2.4.0
    - aiohttp==3.8.1
    - aiosignal==1.2.0
    - asttokens==2.2.1
    - async-timeout==4.0.2
    - attrs==21.4.0
    - backcall==0.2.0
    - cachetools==5.2.0
    - click==8.1.3
    - comm==0.1.2
    - configargparse==1.5.3
    - contourpy==1.0.7
    - cycler==0.11.0
    - dash==2.8.1
    - dash-core-components==2.0.0
    - dash-html-components==2.0.0
    - dash-table==5.0.0
    - debugpy==1.6.6
    - decorator==5.1.1
    - einops==0.6.0
    - einops-exts==0.0.4
    - executing==1.2.0
    - fastjsonschema==2.16.2
    - flask==2.2.2
    - fonttools==4.38.0
    - frozenlist==1.3.0
    - fsspec==2022.5.0
    - google-auth==2.6.6
    - google-auth-oauthlib==0.4.6
    - grpcio==1.46.3
    - imageio==2.19.3
    - importlib-metadata==4.11.4
    - ipykernel==6.21.1
    - ipython==8.10.0
    - ipywidgets==8.0.4
    - itsdangerous==2.1.2
    - jedi==0.18.2
    - jinja2==3.1.2
    - joblib==1.2.0
    - jsonschema==4.17.3
    - jupyter-client==8.0.2
    - jupyter-core==5.2.0
    - jupyterlab-widgets==3.0.5
    - kiwisolver==1.4.4
    - markdown==3.3.7
    - markupsafe==2.1.2
    - matplotlib==3.6.3
    - matplotlib-inline==0.1.6
    - multidict==6.0.2
    - nbformat==5.5.0
    - nest-asyncio==1.5.6
    - networkx==2.8.2
    - oauthlib==3.2.0
    - open3d==0.16.0
    - packaging==21.3
    - pandas==1.4.2
    - parso==0.8.3
    - pexpect==4.8.0
    - pickleshare==0.7.5
    - platformdirs==3.0.0
    - plotly==5.13.0
    - plyfile==0.7.4
    - prompt-toolkit==3.0.36
    - protobuf==3.20.1
    - psutil==5.9.4
    - ptyprocess==0.7.0
    - pure-eval==0.2.2
    - pyasn1==0.4.8
    - pyasn1-modules==0.2.8
    - pydeprecate==0.3.2
    - pygments==2.14.0
    - pyparsing==3.0.9
    - pyquaternion==0.9.9
    - pyrsistent==0.19.3
    - python-dateutil==2.8.2
    - pytorch-lightning==1.6.4
    - pytz==2022.1
    - pywavelets==1.3.0
    - pyyaml==6.0
    - pyzmq==25.0.0
    - requests-oauthlib==1.3.1
    - rotary-embedding-torch==0.2.1
    - rsa==4.8
    - scikit-image==0.19.2
    - scikit-learn==1.2.1
    - scipy==1.8.1
    - stack-data==0.6.2
    - tenacity==8.2.1
    - tensorboard==2.9.0
    - tensorboard-data-server==0.6.1
    - tensorboard-plugin-wit==1.8.1
    - threadpoolctl==3.1.0
    - tifffile==2022.5.4
    - torch-scatter==2.0.9
    - torchmetrics==0.9.0
    - tornado==6.2
    - tqdm==4.64.0
    - traitlets==5.9.0
    - trimesh==3.12.5
    - wcwidth==0.2.6
    - werkzeug==2.2.2
    - widgetsnbextension==4.0.5
    - yarl==1.7.2
    - zipp==3.8.0
prefix: /home/<USER>/.conda/envs/diffusion
