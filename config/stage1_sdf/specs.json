{"Description": "training joint SDF-VAE model for modulating SDFs on the couch dataset", "DataSource": "data", "GridSource": "data/grid_data", "TrainSplit": "data/splits/couch_all.json", "TestSplit": "data/splits/couch_all.json", "training_task": "modulation", "SdfModelSpecs": {"hidden_dim": 512, "latent_dim": 256, "pn_hidden_dim": 128, "num_layers": 9}, "SampPerMesh": 16000, "PCsize": 1024, "num_epochs": 100001, "log_freq": 5000, "kld_weight": 1e-05, "latent_std": 0.25, "sdf_lr": 0.0001}